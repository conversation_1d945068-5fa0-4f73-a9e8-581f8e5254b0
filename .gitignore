# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Database
chroma_db/
*.db
*.sqlite3

# Temporary files
*.tmp
*.temp
/tmp/

# AWS credentials (keep .pem files but ignore other sensitive files)
.env
*.env
aws/output.json
!aws/*.pem

# Backup files
*_backup.py
*.bak
app_older.py

# Lambda deployment packages
*.zip
lambda_package/
lambda_video_processor.zip

# Video files (too large for git)
assets/
*.mp4
*.avi
*.mov
*.mkv

# Cookies and sensitive data
cookies.txt

# Cache files
search_cache.pkl
