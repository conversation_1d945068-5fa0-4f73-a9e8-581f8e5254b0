#!/bin/bash

# Video Search App Startup Script
# This script starts the Streamlit app with proper logging and error handling

echo "🚀 Starting Video Search App..."
echo "📅 $(date)"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "app.py" ]; then
    echo "❌ Error: app.py not found. Please run this script from the project root directory."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Error: Virtual environment not found. Please run 'python -m venv venv' first."
    exit 1
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Check if required packages are installed
echo "📦 Checking dependencies..."
python -c "import streamlit, boto3, chromadb" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Error: Required packages not installed. Please run 'pip install -r requirements.txt'"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  Warning: .env file not found. Make sure AWS credentials are configured."
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Start the Streamlit app with logging
echo "🌟 Starting Streamlit app..."
echo "📊 Logs will be saved to: logs/streamlit.log"
echo "🌐 App will be available at: http://0.0.0.0:8501"
echo "🔐 Password: minfy2025"
echo "=================================="

# Start Streamlit with proper logging
streamlit run app.py \
    --server.address 0.0.0.0 \
    --server.port 8501 \
    --server.headless true \
    --logger.level info \
    --browser.gatherUsageStats false \
    2>&1 | tee logs/streamlit.log

echo "🛑 Streamlit app stopped."
